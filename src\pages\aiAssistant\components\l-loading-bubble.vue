<template>
  <div v-if="loading.show" class="loading-bubble" :class="`stage-${loading.stage}`">
    <div class="loading-content">
      <!-- 加载动画 -->
      <div class="loading-dots">
        <div v-for="i in 3" :key="i" class="dot" 
             :style="{ animationDelay: `${(i - 1) * 0.15}s` }">
        </div>
      </div>
      
      <!-- 加载文案 -->
      <div class="loading-text">{{ loading.text }}</div>
      
      <!-- 任务进度信息 -->
      <div v-if="loading.progress" class="progress-info">
        <span class="step-info">
          步骤 {{ loading.progress.current }}/{{ loading.progress.total }}
        </span>
        <div v-if="loading.progress.percent" class="progress-bar">
          <div class="progress-fill" 
               :style="{ width: `${loading.progress.percent}%` }">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  loading: {
    type: Object,
    required: true
  }
})
</script>

<style lang="scss" scoped>
.loading-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 12px 16px;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  margin-bottom: 12px;
  
  &.stage-thinking {
    border-left: 3px solid var(--color-primary);
  }
  
  &.stage-analyzing {
    border-left: 3px solid var(--color-warning);
  }
  
  &.stage-executing {
    border-left: 3px solid var(--color-success);
  }
  
  &.stage-generating {
    border-left: 3px solid var(--color-info);
  }
  
  &.stage-preparing {
    border-left: 3px solid var(--color-secondary);
  }
  
  .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .loading-dots {
    display: flex;
    gap: 4px;
    
    .dot {
      width: 8px;
      height: 8px;
      background-color: var(--color-gray-400);
      border-radius: 50%;
      animation: loading-pulse 1.4s ease-in-out infinite;
    }
  }
  
  .loading-text {
    font-size: 14px;
    color: var(--color-gray-600);
    flex: 1;
  }
  
  .progress-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
    
    .step-info {
      font-size: 12px;
      color: var(--color-gray-500);
      text-align: right;
    }
    
    .progress-bar {
      height: 4px;
      background-color: var(--color-gray-200);
      border-radius: 2px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background-color: var(--color-success);
        transition: width 0.3s ease;
      }
    }
  }
}

@keyframes loading-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.15);
    opacity: 1;
  }
}
</style>
