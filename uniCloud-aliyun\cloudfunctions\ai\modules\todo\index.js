/**
 * Todo 模块主入口文件
 * 整合认证管理、任务管理、项目管理等功能模块
 * 提供统一的 TodoTool 类接口
 */

const AuthManager = require('./auth')
const TaskManager = require('./tasks')
const ProjectManager = require('./projects')
const { ERROR_CODES } = require('./config')
const { createErrorResponse } = require('./utils')

/**
 * Todo 工具类
 * 封装所有 todolist 相关功能的主类
 */
class TodoTool {
  constructor() {
    // 初始化各个管理器
    this.authManager = new AuthManager()
    this.taskManager = new TaskManager(this.authManager)
    this.projectManager = new ProjectManager(this.authManager)

    // 基础配置
    this.debug = true

    // 调试用的默认测试 token
    this.debugToken =
      '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E'

    // 性能优化：连接复用
    this.connectionPool = new Map()
    this.maxConnections = 10
    this.connectionTimeout = 30 * 1000 // 30 秒连接超时

    // 性能统计
    this.stats = {
      requestCount: 0,
      avgResponseTime: 0,
      lastResetTime: Date.now(),
    }

    // 缓存机制
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5 分钟缓存过期时间

    console.log('[TodoTool] 内置 Todo 工具初始化完成')

    // 如果开启调试模式，自动初始化 token
    if (this.debug) {
      this._initDebugMode()
    }
  }

  /**
   * 调试模式初始化
   * 当 debug 开关为 true 时，自动调用 initWithToken 方法进行初始化
   * @private
   */
  async _initDebugMode() {
    try {
      console.log('[TodoTool] [_initDebugMode] 调试模式已开启，正在使用测试 token 初始化...')
      const result = await this.initWithToken(this.debugToken)
      if (result.errCode) {
        console.error('[TodoTool] [_initDebugMode] 调试模式初始化失败：', result)
      } else {
        console.log('[TodoTool] [_initDebugMode] 调试模式初始化成功')
      }
    } catch (error) {
      console.error('[TodoTool] [_initDebugMode] 调试模式初始化异常：', error)
    }
  }

  /**
   * 统一的工具执行入口
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    const executeStartTime = Date.now()

    console.log(`[TodoTool] [execute] === 开始执行方法 ===`, {
      method,
      parameters: JSON.stringify(parameters, null, 2),
      startTime: new Date(executeStartTime).toISOString(),
      timestamp: executeStartTime,
    })

    try {
      // 确保认证状态
      const authStartTime = Date.now()
      console.log(`[TodoTool] [execute] 开始确保认证状态`, {
        method,
        authStartTime: new Date(authStartTime).toISOString(),
        currentAuthStatus: this.authManager.getAuthStatus(),
      })

      await this.authManager.ensureAuthenticated()

      const authEndTime = Date.now()
      console.log(`[TodoTool] [execute] 认证状态确保完成`, {
        method,
        authDuration: authEndTime - authStartTime,
        authEndTime: new Date(authEndTime).toISOString(),
        finalAuthStatus: this.authManager.getAuthStatus(),
      })

      // 根据方法名调用对应的处理函数
      console.log(`[TodoTool] [execute] 开始路由到具体方法`, {
        method,
        hasParameters: Object.keys(parameters).length > 0,
        parameterKeys: Object.keys(parameters),
      })

      let result
      const methodStartTime = Date.now()

      switch (method) {
        // 认证相关方法
        case 'login':
          console.log(`[TodoTool] [execute] 执行登录方法`, { parameters })
          result = await this.authManager.login(parameters)
          break
        case 'initWithToken':
          console.log(`[TodoTool] [execute] 执行token初始化方法`, {
            hasToken: !!parameters.token,
            tokenLength: parameters.token ? parameters.token.length : 0,
          })
          result = await this.authManager.initWithToken(parameters.token)
          break
        case 'getBatchData':
          console.log(`[TodoTool] [execute] 执行获取批量数据方法`)
          result = await this.authManager.getBatchData()
          break

        // 任务管理方法
        case 'getTasks':
          console.log(`[TodoTool] [execute] 执行获取任务列表方法`, { parameters })
          result = await this.taskManager.getTasks(parameters)
          break
        case 'createTask':
          console.log(`[TodoTool] [execute] !!! 执行创建任务方法 !!!`, {
            parameters: JSON.stringify(parameters, null, 2),
            taskTitle: parameters.title,
            taskContent: parameters.content,
            taskPriority: parameters.priority,
            projectName: parameters.projectName,
            methodStartTime: new Date(methodStartTime).toISOString(),
          })
          result = await this.taskManager.createTask(parameters)
          console.log(`[TodoTool] [execute] !!! 创建任务方法执行完成 !!!`, {
            result: JSON.stringify(result, null, 2),
            isSuccess: result?.errCode === null || result?.errCode === 0,
            errorCode: result?.errCode,
            errorMessage: result?.errMsg,
            createdTaskId: result?.data?.id,
            createdTaskTitle: result?.data?.title,
            methodDuration: Date.now() - methodStartTime,
          })
          break
        case 'updateTask':
          console.log(`[TodoTool] [execute] 执行更新任务方法`, {
            taskId: parameters.taskId,
            updateData: parameters.updateData,
          })
          result = await this.taskManager.updateTask(parameters.taskId, parameters.updateData)
          break
        case 'deleteTask':
          console.log(`[TodoTool] [execute] 执行删除任务方法`, { taskId: parameters.taskId })
          result = await this.taskManager.deleteTask(parameters.taskId)
          break
        case 'getTask':
          console.log(`[TodoTool] [execute] 执行获取单个任务方法`, { taskId: parameters.taskId })
          result = await this.taskManager.getTask(parameters.taskId)
          break

        // 项目管理方法
        case 'getProjects':
          console.log(`[TodoTool] [execute] 执行获取项目列表方法`, { parameters })
          result = await this.projectManager.getProjects(parameters)
          break
        case 'createProject':
          console.log(`[TodoTool] [execute] 执行创建项目方法`, { parameters })
          result = await this.projectManager.createProject(parameters)
          break
        case 'updateProject':
          console.log(`[TodoTool] [execute] 执行更新项目方法`, {
            projectId: parameters.projectId,
            updateData: parameters.updateData,
          })
          result = await this.projectManager.updateProject(parameters.projectId, parameters.updateData)
          break
        case 'deleteProject':
          console.log(`[TodoTool] [execute] 执行删除项目方法`, { projectId: parameters.projectId })
          result = await this.projectManager.deleteProject(parameters.projectId)
          break
        case 'getProject':
          console.log(`[TodoTool] [execute] 执行获取单个项目方法`, { projectId: parameters.projectId })
          result = await this.projectManager.getProject(parameters.projectId)
          break

        default:
          const error = new Error(`未知的方法：${method}`)
          console.error(`[TodoTool] [execute] 未知方法`, {
            method,
            availableMethods: [
              'login',
              'initWithToken',
              'getBatchData',
              'getTasks',
              'createTask',
              'updateTask',
              'deleteTask',
              'getTask',
              'getProjects',
              'createProject',
              'updateProject',
              'deleteProject',
              'getProject',
            ],
            error: error.message,
          })
          throw error
      }

      const executeEndTime = Date.now()
      console.log(`[TodoTool] [execute] === 方法执行成功完成 ===`, {
        method,
        totalDuration: executeEndTime - executeStartTime,
        methodDuration: executeEndTime - methodStartTime,
        endTime: new Date(executeEndTime).toISOString(),
        resultStatus:
          result?.errCode !== undefined
            ? result.errCode === null || result.errCode === 0
              ? 'success'
              : 'error'
            : 'unknown',
        resultErrCode: result?.errCode,
        resultErrMsg: result?.errMsg,
        hasResultData: !!result?.data,
        success: true,
      })

      return result
    } catch (error) {
      const executeEndTime = Date.now()

      console.error(`[TodoTool] [execute] === 方法执行异常 ===`, error, {
        method,
        parameters: JSON.stringify(parameters, null, 2),
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack,
        totalDuration: executeEndTime - executeStartTime,
        endTime: new Date(executeEndTime).toISOString(),
        success: false,
      })

      // 特别关注任务创建相关的错误
      if (method === 'createTask') {
        console.error(`[TodoTool] [execute] !!! 创建任务方法执行失败 !!!`, error, {
          method,
          inputParameters: JSON.stringify(parameters, null, 2),
          errorDetails: {
            name: error.name,
            message: error.message,
            stack: error.stack,
          },
          totalDuration: executeEndTime - executeStartTime,
          timestamp: new Date().toISOString(),
        })
      }

      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }

  // 为了保持向后兼容性，提供直接方法调用接口

  /**
   * 用户登录认证
   * @param {object} options - 登录参数
   * @returns {object} 登录结果
   */
  async login(options = {}) {
    return await this.authManager.login(options)
  }

  /**
   * 使用已有 token 初始化
   * @param {string} token - 访问令牌
   * @returns {object} 初始化结果
   */
  async initWithToken(token) {
    return await this.authManager.initWithToken(token)
  }

  /**
   * 获取所有基础数据（任务、项目、标签）
   * @returns {object} 基础数据
   */
  async getBatchData() {
    return await this.authManager.getBatchData()
  }

  /**
   * 获取任务列表
   * @param {object} options - 查询参数
   * @returns {object} 任务列表
   */
  async getTasks(options = {}) {
    return await this.taskManager.getTasks(options)
  }

  /**
   * 创建任务
   * @param {object} options - 任务数据
   * @returns {object} 创建结果
   */
  async createTask(options = {}) {
    return await this.taskManager.createTask(options)
  }

  /**
   * 更新任务
   * @param {string} taskId - 任务 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateTask(taskId, updateData) {
    return await this.taskManager.updateTask(taskId, updateData)
  }

  /**
   * 删除任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 删除结果
   */
  async deleteTask(taskId) {
    return await this.taskManager.deleteTask(taskId)
  }

  /**
   * 获取单个任务详情
   * @param {string} taskId - 任务 ID
   * @returns {object} 任务详情
   */
  async getTask(taskId) {
    return await this.taskManager.getTask(taskId)
  }

  /**
   * 获取项目列表
   * @param {object} options - 查询参数
   * @returns {object} 项目列表
   */
  async getProjects(options = {}) {
    return await this.projectManager.getProjects(options)
  }

  /**
   * 创建项目
   * @param {object} options - 项目数据
   * @returns {object} 创建结果
   */
  async createProject(options = {}) {
    return await this.projectManager.createProject(options)
  }

  /**
   * 更新项目
   * @param {string} projectId - 项目 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateProject(projectId, updateData) {
    return await this.projectManager.updateProject(projectId, updateData)
  }

  /**
   * 删除项目
   * @param {string} projectId - 项目 ID
   * @returns {object} 删除结果
   */
  async deleteProject(projectId) {
    return await this.projectManager.deleteProject(projectId)
  }

  /**
   * 获取单个项目详情
   * @param {string} projectId - 项目 ID
   * @returns {object} 项目详情
   */
  async getProject(projectId) {
    return await this.projectManager.getProject(projectId)
  }

  /**
   * 性能统计：更新统计信息
   * @param {number} responseTime - 响应时间
   */
  _updateStats(responseTime) {
    this.stats.requestCount++

    // 计算平均响应时间
    this.stats.avgResponseTime =
      (this.stats.avgResponseTime * (this.stats.requestCount - 1) + responseTime) / this.stats.requestCount
  }

  /**
   * 获取性能统计信息
   * @returns {object} 性能统计数据
   */
  getPerformanceStats() {
    const now = Date.now()
    const uptime = now - this.stats.lastResetTime

    return {
      ...this.stats,
      uptime: uptime,
      isAuthenticated: this.authManager.isAuthenticated,
    }
  }

  /**
   * 确保认证状态
   * 实现认证状态复用，避免重复认证
   */
  async ensureAuthenticated() {
    return await this.authManager.ensureAuthenticated()
  }
}

module.exports = TodoTool
